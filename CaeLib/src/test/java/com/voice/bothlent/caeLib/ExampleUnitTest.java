package com.voice.bothlent.caeLib;

import com.voice.bothlent.caeLib.save.PcmSaveMgr;

import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void addition_isCorrect() {
        File file = new File("D:\\pcm\\saoping.wav");
        File file1 = new File("D:\\pcm\\saoping1.pcm");
        try {
            FileInputStream fileOutputStream = new FileInputStream(file);
            FileOutputStream fileOutputStream1 = new FileOutputStream(file1);
            byte[] bytes = new byte[4096];
            int len = 0;
            while ((len = fileOutputStream.read(bytes)) > 0) {
                System.out.println("write====== " + len);
                fileOutputStream1.write(bytes, 0, len);
            }
            fileOutputStream1.flush();
            Thread.sleep(10);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}