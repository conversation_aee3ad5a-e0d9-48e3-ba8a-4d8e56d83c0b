package com.voice.bothlent.caeLib.save;

import android.text.TextUtils;
import android.util.Log;

import com.voice.bothlent.caeLib.service.AudioStreamer;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class PcmSaveMgr {
    private static final String TAG = "PcmSaveMgr";
    private static PcmSaveMgr instance = new PcmSaveMgr();
    private ByteArrayOutputStream bufferAccumulator = new ByteArrayOutputStream();
    private static final int PCM_BUFFER_50MS_SIZE = 12800;
    private static String pcmParentDir;
    private FileChannel fileChannelRaw;
    private FileChannel fileChannelBefore;
    private FileChannel fileChannelAfter;
    private AudioStreamer audioStreamer;
    private final String rawDir = "PcmAudio";
    private final String beforeDir = "CaeRawAudio";
    private final String afterDir = "CaeAsrAudio";
    private final String kailabDir = "KaiAudio";

    private PcmSaveMgr() {
    }

    public static PcmSaveMgr getInstance() {
        return instance;
    }

    public void init(String path) {
        pcmParentDir = path;
        this.audioStreamer = new AudioStreamer();
        audioStreamer.startStreaming("ws://192.168.0.103:8004");
    }

    /**
     * 通过开关控制，这个地方可能会被 多线程访问
     */
    private volatile boolean isSavePcm;

    public boolean isSavePcm() {
        return isSavePcm;
    }

    public void setSavePcm(boolean savePcm) throws FileNotFoundException {
        if (isSavePcm == savePcm) {
            return;
        }
        if (savePcm) { //开始保存
            if (TextUtils.isEmpty(pcmParentDir)) {
                throw new UnsupportedOperationException("must invoke init() before");
            }
            startSave();
        } else { //停止保存
            stopSave();
        }
        isSavePcm = savePcm;
    }

    private void stopSave() {
        if (fileChannelRaw != null) {
            try {
                fileChannelRaw.close();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                fileChannelRaw = null;
            }
        }
        if (fileChannelBefore != null) {
            try {
                fileChannelBefore.close();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                fileChannelBefore = null;
            }
        }
        if (fileChannelAfter != null) {
            try {
                fileChannelAfter.close();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                fileChannelAfter = null;
            }
        }

    }

    private void startSave() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd_hh_mm_ss", Locale.getDefault());
        String fileName = simpleDateFormat.format(new Date());
        fileChannelRaw = openFile(pcmParentDir, rawDir, fileName).getChannel();
        fileChannelBefore = openFile(pcmParentDir, beforeDir, fileName).getChannel();
        fileChannelAfter = openFile(pcmParentDir, afterDir, fileName).getChannel();

        File parentDir = new File(pcmParentDir, kailabDir);
        if (!parentDir.exists()) {
            boolean mkdirs = parentDir.mkdirs();
            if (!mkdirs) {
                Log.e(TAG, "openFile: mkdir exceptions " + parentDir.getAbsolutePath());
            }
        }
    }

    private FileOutputStream openFile(String pcmParentDir, String rawDir, String fileName) {
        File parentDir = new File(pcmParentDir, rawDir);
        if (!parentDir.exists()) {
            boolean mkdirs = parentDir.mkdirs();
            if (!mkdirs) {
                Log.e(TAG, "openFile: mkdir exceptions " + parentDir.getAbsolutePath());
                return null;
            }
        }
        String path = String.format("%s/%s/%s.pcm", pcmParentDir, rawDir, fileName);
        Log.e(TAG, "openFile: " + path);
        File file = new File(path);
        if (file.exists()) {
            boolean delete = file.delete();
            if (!delete) {
                return null;
            }
        }
        try {
            boolean newFile = file.createNewFile();
            if (newFile) {
                return new FileOutputStream(file);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void writeString(byte[] data, int offset, String value) {
        byte[] bytes = value.getBytes();
        System.arraycopy(bytes, 0, data, offset, bytes.length);
    }

    private static void writeInt(byte[] data, int offset, int value) {
        data[offset] = (byte) (value & 0xff);
        data[offset + 1] = (byte) ((value >> 8) & 0xff);
        data[offset + 2] = (byte) ((value >> 16) & 0xff);
        data[offset + 3] = (byte) ((value >> 24) & 0xff);
    }

    private static void writeShort(byte[] data, int offset, short value) {
        data[offset] = (byte) (value & 0xff);
        data[offset + 1] = (byte) ((value >> 8) & 0xff);
    }
    public static byte[] createWavHeader(int totalAudioDataLength, boolean isLastChunk) {
        byte[] header = new byte[44];

        // RIFF chunk descriptor
        writeString(header, 0, "RIFF");
        writeInt(header, 4, 36 + totalAudioDataLength); // tổng kích thước file - 8
        writeString(header, 8, "WAVE");

        // fmt subchunk
        writeString(header, 12, "fmt ");
        writeInt(header, 16, 16); // size of this chunk = 16
        writeShort(header, 20, (short) 1); // PCM format = 1
        writeShort(header, 22, (short) 8); // channels = 8
        writeInt(header, 24, 16000); // sample rate = 16000
        writeInt(header, 28, 256000); // byte rate = 16000 * 8 * 2
        writeShort(header, 32, (short) 16); // block align = 16
        writeShort(header, 34, (short) 16); // bits per sample = 16

        // data subchunk
        writeString(header, 36, "data");
        writeInt(header, 40, totalAudioDataLength); // số byte dữ liệu pcm

        return header;
    }
    public void writeRawPcm(byte[] data, int len) {
//        if (isSavePcm) {
            try {
//                fileChannelRaw.write(ByteBuffer.wrap(data,0,len));

                // Thêm dữ liệu vào buffer tích lũy
                bufferAccumulator.write(data, 0, len);
                // Kiểm tra xem đã đủ 50ms chưa
                if (bufferAccumulator.size() >= PCM_BUFFER_50MS_SIZE) {
                    byte[] pcmData = bufferAccumulator.toByteArray();
                    byte[] wavHeader = createWavHeader(pcmData.length, true);

                    // Tạo tên file WAV
//                    String filename = "recorded_audio_" + System.currentTimeMillis() + ".wav";
//                    String filename2 = "recorded_audio_" + System.currentTimeMillis() + ".pcm";
//                    File fileWav = new File(pcmParentDir + "/KaiAudio", filename);
//                    File filePcm = new File(pcmParentDir + "/KaiAudio", filename2);
//                    FileOutputStream fosWav = new FileOutputStream(fileWav);
//                    FileOutputStream fosPcm = new FileOutputStream(filePcm);
                    // Ghi header rồi đến dữ liệu PCM
//                    fosWav.write(wavHeader);
//                    fosWav.write(pcmData);
//                    fosWav.close();
//                    fosPcm.write(pcmData);
//                    fosPcm.close();

                    // Gửi qua WebSocket
                    Log.e(TAG, "writeRaw Pcm: " + pcmData.length);
                    audioStreamer.sendWebsocketData(pcmData, pcmData.length);
                    // Xóa buffer để bắt đầu tích lũy lại
                    bufferAccumulator.reset();
                }

//                Log.e(TAG, "writeRaw Pcm: " + len);
//                audioStreamer.sendWebsocketData(data, len);
            } catch (Exception e) {
                e.printStackTrace();
            }
//        }
    }

    public void writeBeforePcm(byte[] data, int len) {
        if (isSavePcm) {
            try {
                fileChannelBefore.write(ByteBuffer.wrap(data,0,len));
//                Log.e(TAG, "writeRaw Before Pcm: " + len);
//                audioStreamer.sendWebsocketData(data,len);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void writeAfterPcm(byte[] data, int len) {
        if (isSavePcm) {
            try {
                fileChannelAfter.write(ByteBuffer.wrap(data,0,len));
//                Log.e(TAG, "writeRaw After Pcm: " + len);
//                audioStreamer.sendWebsocketData(data, len);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
