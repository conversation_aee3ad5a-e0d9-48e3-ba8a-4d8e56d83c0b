package com.voice.bothlent.caeLib.checkTask;


import android.content.Context;

import com.voice.bothlent.caeLib.R;
import com.voice.bothlent.caeLib.VoiceEngineMgr;
import com.voice.bothlent.caeLib.checkTask.base.BaseCheckTask;
import com.voice.bothlent.caeLib.checkTask.base.CheckType;

public class CardOpenCheck extends BaseCheckTask {

    public CardOpenCheck(Context mContext) {
        super(mContext);
    }

    @Override
    public void checkHandler() {
        checkDeviceBean = VoiceEngineMgr.getInstance().checkStartRecord();

    }

    @Override
    public void checkStart() {
        checkDeviceBean.setCheckType(CheckType.CARD_OPEN);
        checkDeviceBean.setCheckTitle(mContext.getString(R.string.check_open_card));
    }
}
