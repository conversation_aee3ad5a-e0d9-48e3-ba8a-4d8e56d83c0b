package com.voice.bothlent.caeLib.service;

import android.util.Log;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

public class AudioStreamer {
    private final String TAG = "AudioStreamer";
    private WebSocketClient webSocketClient;
    private boolean isStreaming = false;

    public void startStreaming(String wsUri) {
        connectWebSocket(wsUri);
    }

    public void stopStreaming() {
        if (webSocketClient != null) {
            webSocketClient.close();
            isStreaming = false;
        }
    }

    public boolean isStreaming() {
        return isStreaming;
    }

    public void connectWebSocket(String wsUri) {
        try {
            // Khởi tạo WebSocket
            webSocketClient = new WebSocketClient(new URI(wsUri)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    Log.d(TAG, "Kết nối WebSocket đã mở");
                }

                @Override
                public void onMessage(String message) {
                    Log.d(TAG, "Nhận tin nhắn từ server: " + message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    Log.d(TAG, "WebSocket đóng: " + reason);
                }

                @Override
                public void onError(Exception ex) {
                    Log.e(TAG, "Lỗi WebSocket", ex);
                }
            };

            webSocketClient.connect();
            isStreaming = true;
        } catch (Exception e) {
            Log.e(TAG, "Lỗi khởi tạo stream", e);
        }
    }

    public void sendWebsocketData(byte[] data, int len) {
        // Gửi qua WebSocket
        if (webSocketClient != null && webSocketClient.getConnection().isOpen()) {
            webSocketClient.send(data);
        }
    }

    public void sendWebsocketData2(byte[] data) {
        // Gửi qua WebSocket
        if (webSocketClient != null && webSocketClient.getConnection().isOpen()) {
            webSocketClient.send(data);
        }
    }
}
