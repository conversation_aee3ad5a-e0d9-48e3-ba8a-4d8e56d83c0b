package com.voice.bothlent.caeLib.checkTask;


import android.content.Context;

import com.voice.bothlent.caeLib.R;
import com.voice.bothlent.caeLib.checkTask.base.BaseCheckTask;
import com.voice.bothlent.caeLib.checkTask.base.CheckType;

public class PlatformMicCheck extends BaseCheckTask {
    public PlatformMicCheck(Context mContext) {
        super(mContext);
    }

    @Override
    public void checkHandler() {
        checkDeviceBean.setCheckResult(String.format(
                mContext.getString(R.string.platform_mic),
                "",
                ""
        ));
        checkDeviceBean.setState(1);
    }

    @Override
    public void checkStart() {
        checkDeviceBean.setCheckType(CheckType.PLATFORM_MIC);
        checkDeviceBean.setCheckTitle(mContext.getString(R.string.check_platform_mic));
    }
}
