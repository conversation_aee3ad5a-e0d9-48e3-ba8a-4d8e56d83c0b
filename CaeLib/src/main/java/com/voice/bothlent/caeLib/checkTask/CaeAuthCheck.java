package com.voice.bothlent.caeLib.checkTask;


import android.content.Context;

import com.voice.bothlent.caeLib.R;
import com.voice.bothlent.caeLib.VoiceEngineMgr;
import com.voice.bothlent.caeLib.bean.CheckResultBean;
import com.voice.bothlent.caeLib.checkTask.base.BaseCheckTask;
import com.voice.bothlent.caeLib.checkTask.base.CheckType;

public class CaeAuthCheck extends BaseCheckTask {
    private String mSN;

    public CaeAuthCheck(Context mContext, String sn) {
        super(mContext);
        mSN = sn;
    }

    @Override
    public void checkHandler() {
        CheckResultBean temp = VoiceEngineMgr.getInstance().checkEngine(mSN);
        checkDeviceBean.setState(temp.getState());
        checkDeviceBean.setCheckResult(temp.getCheckResult());
    }

    @Override
    public void checkStart() {
        checkDeviceBean.setCheckType(CheckType.CAE_AUTH);
        checkDeviceBean.setCheckTitle(mContext.getString(R.string.check_cae_auth));
    }
}
