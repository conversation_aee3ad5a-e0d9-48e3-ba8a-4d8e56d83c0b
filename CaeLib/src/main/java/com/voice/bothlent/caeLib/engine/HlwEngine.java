package com.voice.bothlent.caeLib.engine;

import android.content.Context;
import android.util.Log;

import com.iflytek.iflyos.cae.CAE;
import com.voice.bothlent.caeLib.bean.CheckResultBean;
import com.voice.bothlent.caeLib.bean.WakeInfo;
import com.voice.bothlent.caeLib.utils.FileUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class HlwEngine extends BaseEngine {
    private static final String configCAE_RES = "CAE_RES";
    private static final String configIVW_RES = "IVW_RES";
    private static final String configMicNum = "nMicNum";
    private static final String paramHlwName = "hlw.param";
    private static final String iniHlwName = "hlw.ini";
    private static final String assetParentPath = "hlw/";
    private static final String TAG = "HlwEngine";

    static {
        CAE.loadLib("hlw");
    }


    @Override
    protected WakeInfo parseWakeInfo(String s) {
        // {"cur_ms":288768, "start_ms":287290, "end_ms":288310, "beam":1, "physical":1, "similar":1498.000000, "similar_thresh":900.000000, "power":1210436681728.000000, "angle":75.000000, "keyword":"ni3 hao3 hua1 sheng1"}
        JSONObject jsonObject = null;
        WakeInfo wakeInfo = new WakeInfo();
        try {
            jsonObject = new JSONObject(s);
            int beam = jsonObject.optInt("physical");
            int angle = jsonObject.optInt("angle");
            int similar = jsonObject.optInt("similar");
            wakeInfo.setBeam(beam);
            wakeInfo.setAngle(angle);
            wakeInfo.setScore(similar);
        } catch (JSONException e) {
            e.printStackTrace();
            wakeInfo.setAngle(0);
            wakeInfo.setBeam(0);
        }
        return wakeInfo;
    }

    @Override
    public CheckResultBean init(Context context, String sn) {
        //res check
        CheckResultBean checkResult = new CheckResultBean();
        List<String> keyList = Arrays.asList(configCAE_RES, configIVW_RES);
        Map<String, String> stringStringMap = FileUtils.readAssetValueByKey(context,
                assetParentPath + iniHlwName, keyList);

        if (stringStringMap != null && stringStringMap.size() == keyList.size()) {

            try {
                // Replace {APP_FILES_DIR} with actual app files directory
                String appFilesDir = context.getFilesDir().getAbsolutePath();
                String cae_res_path_original = stringStringMap.get(configCAE_RES);
                String ivw_res_path_original = stringStringMap.get(configIVW_RES);

                Log.d(TAG,"App files dir = " + appFilesDir);
                Log.d(TAG,"CAE路径 original = " + cae_res_path_original);
                Log.d(TAG,"IVW路径 original = " + ivw_res_path_original);

                String cae_res_path = cae_res_path_original.replace("{APP_FILES_DIR}", appFilesDir);
                String ivw_res_path = ivw_res_path_original.replace("{APP_FILES_DIR}", appFilesDir);

                Log.d(TAG,"CAE路径 after replace = "+cae_res_path);
                Log.d(TAG,"IVW路径 after replace = "+ivw_res_path);

                checkResource(context, cae_res_path, assetParentPath);
                checkResource(context, ivw_res_path, assetParentPath);

                configParentPath = new File(ivw_res_path).getParentFile().getAbsolutePath();
                Log.d(TAG,"路径configParentPath = "+cae_res_path);
                File hlwIniFile = new File(configParentPath, iniHlwName);
                if (!hlwIniFile.exists()) {
                    Log.d(TAG,"copy hlw file: "+ iniHlwName);
                    FileUtils.copyAssets2Sdcard(context, assetParentPath, hlwIniFile.getName(), configParentPath);
                    // Replace {APP_FILES_DIR} in the copied file
                    replaceAppFilesDirInFile(hlwIniFile, appFilesDir);
                }
                File paramFile = new File(configParentPath, paramHlwName);
                if (!paramFile.exists()) {
                    Log.d(TAG,"copy param file: "+ paramHlwName);
                    FileUtils.copyAssets2Sdcard(context, assetParentPath, paramFile.getName(), configParentPath);
                }
                String iniPath = configParentPath + File.separator + iniHlwName;
                String paramPath = configParentPath + File.separator + paramHlwName;

                // step1 : 根据配置资源进行初始化
                int isInit = CAE.CAENew(iniPath, paramPath, mCAEListener);
                if (isInit == 0) {
                    // step2 : 根据授权码进行授权，授权正确后，即可以使用
                    String version = CAE.CAEGetVersion();
                    Log.e(TAG, "init: " + sn + "   " + version);
                    int auth = CAE.CAEAuth(sn);
                    if (auth >= 0) {
                        checkResult.setState(1);
                        List<String> keyListMicNum = Collections.singletonList(configMicNum);
                        Map<String, String> MicNumMap = FileUtils.readAssetValueByKey(context,
                                assetParentPath + new File(cae_res_path).getName(), keyListMicNum);
                        checkResult.setCheckResult(String.format(Locale.getDefault(), "%s,%s,micNum=%s",  version,cae_res_path, MicNumMap.get(configMicNum)));
                    } else {
                        checkResult.setState(0);
                        checkResult.setCheckResult("cae auth error, code  = " + auth);
                    }
                } else {
                    checkResult.setState(0);
                    checkResult.setCheckResult("cae init error,please check res file " + iniPath + ";" + paramPath);
                }

            } catch (IOException ignored) {
                checkResult.setState(0);
                checkResult.setCheckResult("copy res file error " + ignored);
            }
        } else {
            checkResult.setState(0);
            checkResult.setCheckResult("hlw.ini config error");
        }
        return checkResult;
    }


    @Override
    public void setBeam(int beam) {
        CAE.CAESetRealBeam(beam);
    }


    @Override
    public void writeAudio(byte[] audio, int len) {
        CAE.CAEAudioWrite(audio, len);
//        Log.e(TAG, "writeAudio: " + len);
    }


    @Override
    public void setShowLog(boolean show) {
        CAE.CAESetShowLog(show ? 0 : 1);
    }

    @Override
    public void onDestroy() {
        CAE.CAEDestory();
    }

    private void replaceAppFilesDirInFile(File file, String appFilesDir) {
        try {
            // Read file content
            StringBuilder content = new StringBuilder();
            java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(file));
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line.replace("{APP_FILES_DIR}", appFilesDir)).append("\n");
            }
            reader.close();

            // Write back to file
            java.io.FileWriter writer = new java.io.FileWriter(file);
            writer.write(content.toString());
            writer.close();

            Log.d(TAG, "Replaced {APP_FILES_DIR} in file: " + file.getAbsolutePath());
        } catch (Exception e) {
            Log.e(TAG, "Error replacing {APP_FILES_DIR} in file: " + file.getAbsolutePath(), e);
        }
    }
}
