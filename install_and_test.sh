#!/bin/bash

echo "🚀 Installing and Testing AoboSdkDemo..."

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device connected. Please connect a device and enable USB debugging."
    exit 1
fi

# Install APK
echo "📱 Installing APK..."
adb install -r app/build/outputs/apk/debug/app-debug.apk

if [ $? -eq 0 ]; then
    echo "✅ APK installed successfully!"
    
    # Note: CAE directories are now created automatically in app's internal storage
    echo "📁 CAE directories will be created automatically in app's internal storage"
    
    # Grant permissions
    echo "🔐 Granting permissions..."
    adb shell "pm grant com.vetc.aobot android.permission.RECORD_AUDIO"
    adb shell "pm grant com.vetc.aobot android.permission.WRITE_EXTERNAL_STORAGE"
    adb shell "pm grant com.vetc.aobot android.permission.READ_EXTERNAL_STORAGE"
    
    echo "🎯 Starting application..."
    adb shell "am start -n com.vetc.aobot/.MainActivity"
    
    echo "📋 Monitoring logs (press Ctrl+C to stop)..."
    adb logcat -c  # Clear logs
    adb logcat | grep -E "(MainActivity|CAE|Alsa|hlw|tinyalsa)"
    
else
    echo "❌ Failed to install APK"
    exit 1
fi
