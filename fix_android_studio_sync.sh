#!/bin/bash

echo "🔧 Fixing Android Studio Gradle Sync Issues..."

# Set Java 17 environment
export JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home

echo "📍 Using Java: $JAVA_HOME"

# Clean all caches
echo "🧹 Cleaning Gradle caches..."
rm -rf ~/.gradle/caches
rm -rf ~/.gradle/daemon
rm -rf .gradle
rm -rf build
rm -rf app/build
rm -rf CaeLib/build

# Clean Android Studio caches
echo "🧹 Cleaning Android Studio caches..."
rm -rf ~/.android/build-cache

# Invalidate caches in Android Studio
echo "📝 Instructions for Android Studio:"
echo "1. Open Android Studio"
echo "2. Go to File > Invalidate Caches and Restart"
echo "3. Choose 'Invalidate and Restart'"
echo "4. After restart, try Gradle Sync again"

echo "✅ Cache cleanup completed!"
echo ""
echo "🔧 Additional steps if sync still fails:"
echo "1. In Android Studio: File > Settings > Build > Gradle"
echo "2. Set 'Gradle JDK' to Java 17"
echo "3. Set 'Use Gradle from' to 'gradle-wrapper.properties file'"
echo "4. Click Apply and OK"
echo "5. Try sync again"
