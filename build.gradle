// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.8.10'

    repositories {
        google()
        mavenCentral()
        maven { url 'https://aws.amazon.com/android-sdk/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

configurations.all {
    resolutionStrategy.force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.10'
    resolutionStrategy.force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10'
    resolutionStrategy.force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10'
    resolutionStrategy.force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10'
}