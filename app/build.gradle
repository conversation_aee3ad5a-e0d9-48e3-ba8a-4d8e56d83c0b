plugins {
    id 'com.android.application'
}

android {
    namespace 'com.vetc.aobot'
    compileSdk 33

    defaultConfig {
        applicationId "com.vetc.aobot"
        minSdk 23
        targetSdk 33
        versionCode 1
        versionName "1.0.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters "armeabi-v7a"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    repositories {
        flatDir { dirs 'libs' }
    }

    buildToolsVersion "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}


configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.jetbrains.kotlin') {
            details.useVersion '1.8.10'
            details.because "Chúng ta chỉ dùng Kotlin 1.8.10 để tránh xung đột"
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    // Kotlin standard library – dùng chung một phiên bản
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.8.10'

    // AndroidX Libraries (thay thế cho Support Library)
    implementation 'androidx.core:core-ktx:1.10.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'

    // Thư viện AAR SDK
    //    implementation project(':aoborobotsdk-v1.9')
    implementation(name: 'aoborobotsdk-v1.9', ext: 'aar')
    implementation 'org.greenrobot:eventbus:3.3.1'

    // Thư viện xử lý lọc nhiễu âm thanh
    implementation 'org.apache.commons:commons-math3:3.6.1'

    // AWS IoT
    implementation 'com.amazonaws:aws-iot-device-sdk-java:1.3.9'
    implementation 'com.amazonaws:aws-iot-device-sdk-java-samples:1.3.9'

    // ONNX Runtime
    implementation 'com.microsoft.onnxruntime:onnxruntime-android:1.15.0'
    implementation project(':CaeLib') // Phiên bản CPU cho Android

    // Test dependencies
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}