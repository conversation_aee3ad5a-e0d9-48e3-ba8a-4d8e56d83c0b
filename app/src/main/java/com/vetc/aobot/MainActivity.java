package com.vetc.aobot;

import android.Manifest;
import android.app.ProgressDialog;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.net.Uri;
import android.net.nsd.NsdManager;
import android.net.nsd.NsdServiceInfo;
import android.os.Build;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;

import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import com.aobo.aoborobotsdk.agent.RPSlamwareSdpAgent;
import com.aobo.aoborobotsdk.events.ConnectedEvent;
import com.aobo.aoborobotsdk.events.ConnectionFailedEvent;
import com.aobo.aoborobotsdk.events.ConnectionLostEvent;
import com.aobo.aoborobotsdk.events.GetUpdateMapStatusEvent;
import com.aobo.aoborobotsdk.events.LaserScanUpdateEvent;
import com.aobo.aoborobotsdk.events.LineUpdateEvent;
import com.aobo.aoborobotsdk.events.MapUpdateEvent;
import com.aobo.aoborobotsdk.events.MoveActionUpdateEvent;
import com.aobo.aoborobotsdk.events.RobotHealthInfoEvent;
import com.aobo.aoborobotsdk.events.RobotPoseUpdateEvent;
import com.aobo.aoborobotsdk.events.RobotStatusUpdateEvent;
import com.aobo.aoborobotsdk.events.WallUpdateEvent;
import com.aobo.aoborobotsdk.impl.AoboNaviListener;
import com.aobo.aoborobotsdk.platform.AoboRobotManeger;
import com.slamtec.slamware.action.Path;
import com.slamtec.slamware.message.DepthCameraFrame;
import com.slamtec.slamware.robot.LaserPoint;
import com.slamtec.slamware.robot.LaserScan;
import com.slamtec.slamware.robot.Pose;
import com.vetc.aobot.service.AwsMqttClient;
import com.vetc.aobot.service.NetworkUtils;
import com.vetc.aobot.utils.FileUtils;
import com.voice.bothlent.caeLib.VoiceEngineMgr;
import com.voice.bothlent.caeLib.bean.CheckResultBean;
import com.voice.bothlent.caeLib.bean.PcmDevice;
import com.voice.bothlent.caeLib.bean.WakeInfo;
import com.voice.bothlent.caeLib.checkTask.CardNumberCheck;
import com.voice.bothlent.caeLib.checkTask.NetCheck;
import com.voice.bothlent.caeLib.checkTask.PlatformMicCheck;
import com.voice.bothlent.caeLib.checkTask.CardOpenCheck;
import com.voice.bothlent.caeLib.checkTask.CaeAuthCheck;
import com.voice.bothlent.caeLib.checkTask.CardPermissionCheck;
import com.voice.bothlent.caeLib.checkTask.SystemTimeCheck;
import com.voice.bothlent.caeLib.checkTask.TaskCheckMgr;
import com.voice.bothlent.caeLib.checkTask.base.BaseCheckTask;
import com.voice.bothlent.caeLib.checkTask.base.ICheckTaskCallBack;
import com.voice.bothlent.caeLib.dataAdapter.BothlentAdapter;
import com.voice.bothlent.caeLib.engine.HlwEngine;
import com.voice.bothlent.caeLib.engine.IEngineCallBack;
import com.voice.bothlent.caeLib.record.BothlentRecord;
import com.voice.bothlent.caeLib.utils.ShellUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import java.util.Vector;

import android.media.MediaRecorder;

/** @noinspection ALL*/
public class MainActivity extends AppCompatActivity implements AoboNaviListener {
    public static String TAG = "MainActivity";

    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_PERMISSION_CODE = 2;
    private static final int REQUEST_RECORD_AUDIO_PERMISSION = 3;
    private ImageView imageView;
    private Uri photoURI;
    private AoboRobotManeger aoboRobotManeger = AoboRobotManeger.getInstance();
    private VoiceEngineMgr voiceEngineMgr;
    private NsdManager nsdManager;
    private NsdManager.DiscoveryListener discoveryListener;
    ProgressDialog progressBar;
    private MediaRecorder mediaRecorder;
    private boolean isRecording = false;
    private boolean isMapLoaded = false;
    private String audioFilePath;
    private ArrayList<BaseCheckTask> taskList = new ArrayList<>();

    private DepthCameraFrame depthCameraFrame = new DepthCameraFrame();
    protected static RPSlamwareSdpAgent agent;
    private static final String[] NEEDED_PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Button btnPointA = findViewById(R.id.gotoPointA);
        Button btnPointB = findViewById(R.id.gotoPointB);
        Button btnPointC = findViewById(R.id.gotoPointC);
        Button btnPointD = findViewById(R.id.gotoPointD);
        Button btnOpenMap = findViewById(R.id.openMap);

        Log.d(MainActivity.class.getName(), "Ứng dụng đã khởi động");

        AwsMqttClient awsMqttClient = new AwsMqttClient();
        awsMqttClient.setPrivateManager(aoboRobotManeger);
        awsMqttClient.connect(MainActivity.this);
        Log.d(MainActivity.class.getName(), "Kết nối Iot xong");

        aoboRobotManeger.setOnAoboNaviListener(MainActivity.this);

        if (Build.VERSION.SDK_INT >= 23) {
            if (!checkPermissions(NEEDED_PERMISSIONS)) {
                ActivityCompat.requestPermissions(this, NEEDED_PERMISSIONS, 0);
                Log.d(MainActivity.class.getName(), "Yêu cầu quyền truy cập");
                return;
            }
        }

        progressBar = new ProgressDialog(this);
        Button login = (Button) findViewById(R.id.connect);
        login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                connectToRobot();
            }
        });
        Log.d(MainActivity.class.getName(), "Khai báo kết nối robot xong");

        btnPointA.setOnClickListener(v -> moveToTarget(2.125668f, 1.96578f, 0.60382056f, "1.stcm"));
        btnPointB.setOnClickListener(v -> moveToTarget(1.1528608f, 2.3059614f, 2.6524053f, "1.stcm"));
        btnPointC.setOnClickListener(v -> moveToTarget(1.0476071f, 0.11417694f, -1.7067538f, "1.stcm"));
        btnPointD.setOnClickListener(v -> moveToTarget(0.58982337f, 0.99554116f, 2.8707914f, "1.stcm"));
//        btnOpenCamera.setOnClickListener(v -> openCamera());

        btnOpenMap.setOnClickListener(v -> openMapActivity());

        Log.d(MainActivity.class.getName(), "Đã khai báo xong các hàm số");

        initAndLoadMap(MainActivity.this);

        //设置语音引擎的回调
        VoiceEngineMgr.setSoftGain(1.f);
        voiceEngineMgr = VoiceEngineMgr.getInstance();

        // Initialize CAE directories and files
        initCaeDirectories();

        initVoiceSdk();

        // mDNS Service
        nsdManager = (NsdManager) getSystemService(Context.NSD_SERVICE);
        startDiscovery();
    }

    public void startDiscovery() {
        discoveryListener = new NsdManager.DiscoveryListener() {
            @Override
            public void onStartDiscoveryFailed(String serviceType, int errorCode) {
                Log.e("mDNS", "Start discovery failed with error code ["  + errorCode + "] for service type [" + serviceType + "]");
            }

            @Override
            public void onStopDiscoveryFailed(String serviceType, int errorCode) {
                Log.e("mDNS", "Stop discovery failed");
            }

            @Override
            public void onDiscoveryStarted(String serviceType) {
                Log.d("mDNS", "Bắt đầu dò tìm...");
            }

            @Override
            public void onDiscoveryStopped(String serviceType) {
                Log.d("mDNS", "Dừng dò tìm");
            }

            @Override
            public void onServiceFound(NsdServiceInfo serviceInfo) {
                Log.d("mDNS", "Tìm thấy dịch vụ: " + serviceInfo.getServiceName() + " với loại " + serviceInfo.getServiceType());
                if (serviceInfo.getServiceType().equals("_robot._tcp.")) {
//                    nsdManager.resolveService(serviceInfo, resolveListener);
                }
            }

            @Override
            public void onServiceLost(NsdServiceInfo serviceInfo) {
                Log.e("mDNS", "Mất kết nối với dịch vụ: " + serviceInfo.getServiceName());
            }
        };

        nsdManager.discoverServices("_robot._tcp", NsdManager.PROTOCOL_DNS_SD, discoveryListener);
    }

    NsdManager.ResolveListener resolveListener = new NsdManager.ResolveListener() {
        @Override
        public void onResolveFailed(NsdServiceInfo serviceInfo, int errorCode) {
            Log.e("mDNS", "Resolve thất bại");
        }

        @Override
        public void onServiceResolved(NsdServiceInfo serviceInfo) {
            Log.i("mDNS", "Tên: " + serviceInfo.getServiceName());
            Log.i("mDNS", "IP: " + serviceInfo.getHost().getHostAddress());
            Log.i("mDNS", "Cổng: " + serviceInfo.getPort());

            // Kết nối TCP tới server
            connectToServer(serviceInfo.getHost().getHostAddress(), serviceInfo.getPort());
        }
    };

    private void connectToServer(String ip, int port) {
        new Thread(() -> {
            try {
                Socket socket = new Socket(ip, port);
                OutputStream out = socket.getOutputStream();
                InputStream in = socket.getInputStream();

                String msg = "Hello từ Android";
                out.write(msg.getBytes());
                out.flush();

                byte[] buffer = new byte[1024];
                int bytes = in.read(buffer);
                String response = new String(buffer, 0, bytes);
                Log.d("TCP", "Phản hồi từ server: " + response);

                socket.close();
            } catch (Exception e) {
                Log.e("TCP", "Lỗi kết nối", e);
            }
        }).start();
    }

    private void initCaeDirectories() {
        try {
            // Create cae directory in app's internal files directory
            File caeDir = new File(getFilesDir(), "cae");
            if (!caeDir.exists()) {
                caeDir.mkdirs();
                Log.d("MainActivity", "Created CAE directory: " + caeDir.getAbsolutePath());
            }

            // Create vtn directory in app's internal files directory
            File vtnDir = new File(getFilesDir(), "vtn");
            if (!vtnDir.exists()) {
                vtnDir.mkdirs();
                Log.d("MainActivity", "Created VTN directory: " + vtnDir.getAbsolutePath());
            }

            Log.d("MainActivity", "CAE directories initialized successfully");
            Log.d("MainActivity", "App files dir: " + getFilesDir().getAbsolutePath());
        } catch (Exception e) {
            Log.e("MainActivity", "Error creating CAE directories", e);
        }
    }

    private void initVoiceSdk() {
        //1. 初始化降噪算法
        VoiceEngineMgr instance = VoiceEngineMgr.getInstance();
        BothlentRecord baseRecord = new BothlentRecord(new PcmDevice(ShellUtil.fetchCard()));
        instance.init(this, new HlwEngine(), baseRecord, new BothlentAdapter());

        //2. 添加自检任务
        Context applicationContext = this.getApplicationContext();
        if (baseRecord.isNeedCheck()) {
            taskList.add(new CardNumberCheck(applicationContext));
            taskList.add(new CardPermissionCheck(applicationContext));
        }
        taskList.add(new NetCheck(applicationContext));
        taskList.add(new PlatformMicCheck(applicationContext));
        taskList.add(new SystemTimeCheck(applicationContext));

        taskList.add(new CaeAuthCheck(applicationContext, FileUtils.getAssetsContent(this, "auth.ini")));
        taskList.add(new CardOpenCheck(applicationContext));

        startCheckTask();
    }

    private void startCheckTask() {
        TaskCheckMgr instance = TaskCheckMgr.getInstance();
        instance.addTask(taskList);
        instance.startCheckTask(new ICheckTaskCallBack() {
            @Override
            public void onAllTaskCheckEnd() {
                for (BaseCheckTask it : taskList) {
                    if (!it.isCheckSuccess()) {
                        Log.e(TAG, "onAllTaskCheckEnd: " + it.isCheckSuccess());
                    }
                }
                runOnUiThread(() -> {
                    voiceEngineMgr.startWork(new IEngineCallBack() {
                        @Override
                        public void onWakeup(WakeInfo wakeInfo) {
                            Log.e(TAG, "onWakeup: " + wakeInfo);
                        }

                        @Override
                        public void onAudioCallback(byte[] bytes, int len) {
                        }
                    });
                });
            }

            @Override
            public void onCheckStart(CheckResultBean CheckResult) {
                Log.e(TAG, "onCheckStart: " + CheckResult);
            }

            @Override
            public void onCheckEnd(CheckResultBean CheckResult) {
                Log.e(TAG, "onCheckEnd: " + CheckResult);
            }
        });
    }

    private void openMapActivity() {
        if (isMapLoaded)
            gotoMainActivity();
    }

    private void moveToTarget(Float x, Float y, Float yaw, String mapname) {
        Pose pose = new Pose(x,y,0f,yaw,0f,0f);
         aoboRobotManeger.moveToPoint(pose, mapname, false, false);
    }
    private boolean checkPermissions(String[] neededPermissions) {
        if (neededPermissions == null || neededPermissions.length == 0) {
            return true;
        }
        boolean allGranted = true;
        for (String neededPermission : neededPermissions) {
            allGranted &= ContextCompat.checkSelfPermission(this, neededPermission) == PackageManager.PERMISSION_GRANTED;
        }
        return allGranted;
    }

    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        EventBus.getDefault().unregister(this);
        super.onStop();

        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Quyền được cấp, bắt đầu ghi âm
            } else {
                Toast.makeText(this, "Cần quyền ghi âm để sử dụng tính năng này", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void openCamera() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.CAMERA},
                    REQUEST_PERMISSION_CODE);

        } else {
            openCamera2();
        }
    }

    private void openCamera2(){
        ContentValues values = new ContentValues();
        values.put(MediaStore.Images.Media.TITLE, "New Picture");
        values.put(MediaStore.Images.Media.DESCRIPTION, "From the Camera");

        // Camera intent
        Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

        // Set filename
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String vFilename = "FOTO_" + timeStamp + ".jpg";

        // Set directory folder
        File file = new File(Environment.getExternalStorageDirectory() + "/aoborobot/", vFilename);
        Uri imageUri = FileProvider.getUriForFile(this, getApplicationContext().getPackageName() + ".provider", file);

        cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
        startActivityForResult(cameraIntent, REQUEST_IMAGE_CAPTURE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d(MainActivity.class.getName(),"kết quả" + resultCode + ". " + data.getAction());

        if (requestCode == REQUEST_IMAGE_CAPTURE && resultCode == RESULT_OK) {
            imageView.setImageURI(photoURI);
            Log.d("CameraApp", "Ảnh đã được chụp và lưu tại: " + photoURI.getPath());
        }
    }

    public void connectToRobot() {
        progressBar.show();
//        aoboRobotManeger.connect();
        if (agent == null) {
            agent = RPSlamwareSdpAgent.getAgent();
        }
        aoboRobotManeger.setSlamtecIp("************");

        NetworkUtils.postData("Bắt đầu kết nối đến Robot");

        aoboRobotManeger.connect();
//        aoboRobotManeger.getAgent().connectTo("************",1445);

        NetworkUtils.postData("Đã gửi lệnh kết nối thiết bị xong");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ConnectedEvent event) {
        progressBar.dismiss();
        NetworkUtils.postData("Kết nối thiết bị hoàn tất");
        isMapLoaded = true;

        File mapsDir = new File(this.getFilesDir(), "robot_maps");
        Boolean mapLoadedStatus = aoboRobotManeger.loadMap(mapsDir.getAbsolutePath(), "1.stcm");

        NetworkUtils.postData("Kết quả kết nối map: " + mapLoadedStatus);
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ConnectionFailedEvent event) {
        Log.i("TAG", "连接出错");
    }

    private void gotoMainActivity() {
        startActivity(new Intent(this, MapActivity.class));
        finish();
    }

    public void initAndLoadMap(Context context) {
        File mapsDir = new File(context.getFilesDir(), "robot_maps");
        if (!mapsDir.exists()) {
            mapsDir.mkdirs();
        }

        File destinationFile = new File(mapsDir, "1.stcm");
        boolean copied = copyAssetToFile("maps/1.stcm", destinationFile);

        if (copied) {
            NetworkUtils.postData("Bản đồ đã được tạo và lưu vào: " + destinationFile.getAbsolutePath());
        } else {
            NetworkUtils.postData("Không thể sao chép file bản đồ từ assets");
        }
    }

    private boolean copyAssetToFile(String assetFilename, File outFile) {
        AssetManager assetManager = getAssets();
        try (InputStream in = assetManager.open(assetFilename);
             OutputStream out = new FileOutputStream(outFile)) {

            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void getUpdateMapStatus(GetUpdateMapStatusEvent getUpdateMapStatusEvent) {

    }

    @Override
    public void getRobotHealthInfo(RobotHealthInfoEvent robotHealthInfoEvent) {

    }

    @Override
    public void getWallUpdate(WallUpdateEvent wallUpdateEvent) {

    }

    @Override
    public void getLineUpdate(LineUpdateEvent lineUpdateEvent) {

    }

    @Override
    public void getRobotStatusUpdate(RobotStatusUpdateEvent robotStatusUpdateEvent) {
        robotStatusUpdateEvent.getBatteryPercentage();
        NetworkUtils.postData("Robot status: " +
                "Charging [" + robotStatusUpdateEvent.isCharging() + "], " +
                "Battery [" + robotStatusUpdateEvent.getBatteryPercentage() + "], " +
                "Localization quality [" + robotStatusUpdateEvent.getLocalizationQuality() + "]");
    }

    @Override
    public void getRobotPoseUpdate(RobotPoseUpdateEvent robotPoseUpdateEvent) {

    }

    @Override
    public void getMoveActionUpdateEvent(MoveActionUpdateEvent moveActionUpdateEvent) {
        Path path = moveActionUpdateEvent.getRemainingPath();
        if (path.getPoints().isEmpty()) {
            NetworkUtils.postData("Robot đã đến đích");
            NetworkUtils.requestSpeech("Robot đã đến đích");
        }
    }

    @Override
    public void getMapUpdateEvent(MapUpdateEvent mapUpdateEvent) {

    }

    @Override
    public void getLaserScanUpdateEvent(LaserScanUpdateEvent laserScanUpdateEvent) {
        LaserScan laserScan = laserScanUpdateEvent.getLaserScan();
        Vector<LaserPoint> laserPoints = laserScan.getLaserPoints();
        NetworkUtils.postData("Laser scan: " + laserPoints.size() + " points; " +
                "and first Point is ["
                + laserPoints.get(0).getDistance() + ", "
                + laserPoints.get(0).getAngle() + "]");
    }

    @Override
    public void getConnectionLostEvent(ConnectionLostEvent connectionLostEvent) {

    }

    @Override
    public void getConnectionFailedEvent(ConnectionFailedEvent connectionFailedEvent) {

    }

    @Override
    public void getConnected(ConnectedEvent connectedEvent) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (voiceEngineMgr != null) {
            voiceEngineMgr.stopWork();
        }
    }
}
