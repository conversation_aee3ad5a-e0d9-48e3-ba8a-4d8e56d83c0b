package com.vetc.aobot.service;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class ModelLoader {
    private static final String TAG = "ModelLoader";
    
    public static boolean loadModelsFromAssets(Context context) {
        try {
            // Tạo thư mục asr trong filesDir
            File asrDir = new File(context.getFilesDir(), "asr");
            if (!asrDir.exists()) {
                asrDir.mkdirs();
            }

            // Danh sách các mô hình cần tải
            String[] modelFiles = {
                "whisper_encoder.onnx",
                "whisper_decoder.onnx"
            };
            
            // Tải từng mô hình
            for (String modelFile : modelFiles) {
                File outFile = new File(context.getFilesDir(), modelFile);
                
                // Ki<PERSON>m tra xem mô hình đã tồn tại chưa
                if (!outFile.exists()) {
                    // <PERSON><PERSON> chép từ assets sang thư mục files
                    copyAsset(context, "asr/" + modelFile, outFile);
                    Log.d(TAG, "Copied model: " + modelFile);
                }
            }
            
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Error loading models", e);
            return false;
        }
    }
    
    private static void copyAsset(Context context, String assetName, File outFile) throws IOException {
        try (InputStream in = context.getAssets().open(assetName);
             OutputStream out = new FileOutputStream(outFile)) {
            
            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            out.flush();
        }
    }
}