package com.vetc.aobot.service;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Build;
import android.util.Log;

import com.amazonaws.services.iot.client.AWSIotMessage;
import com.amazonaws.services.iot.client.AWSIotMqttClient;
import com.amazonaws.services.iot.client.AWSIotQos;
import com.amazonaws.services.iot.client.AWSIotTopic;
import com.amazonaws.services.iot.client.sample.sampleUtil.PrivateKeyReader;
import com.aobo.aoborobotsdk.platform.AoboRobotManeger;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.util.List;
import java.util.logging.Logger;

public class AwsMqttClient {
    AoboRobotManeger robotManager;

    public void setPrivateManager(AoboRobotManeger rm) {
        this.robotManager = rm;
    }

    public class MyMessage extends AWSIotMessage {
        public MyMessage(String topic, AWSIotQos qos, String payload) {
            super(topic, qos, payload);
        }

        @Override
        public void onSuccess() {
            // called when message publishing succeeded
        }

        @Override
        public void onFailure() {
            // called when message publishing failed
        }

        @Override
        public void onTimeout() {
            // called when message publishing timed out
        }
    }

    public static class KeyStorePasswordPair {
        public KeyStore keyStore;
        public String keyPassword;

        public KeyStorePasswordPair(KeyStore keyStore, String keyPassword) {
            this.keyStore = keyStore;
            this.keyPassword = keyPassword;
        }
    }

    private static InputStream inputStreamFromAssetFile(Context context, String assetPath) throws IOException {
        return context.getAssets().open(assetPath);
    }
    private static List<Certificate> loadCertificates(final InputStream certStream) {
        try {
            final CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
            return (List<Certificate>) certFactory.generateCertificates(certStream);
        } catch (CertificateException e) {
            System.out.println("Failed to load certificate file");
        }
        return null;
    }

    public static KeyStorePasswordPair getKeyStorePasswordPair(final InputStream certIs, final InputStream privateKeyIs) {
        final List<Certificate> certChain = loadCertificates(certIs);
        if (certChain == null || privateKeyIs == null) return null;
        final PrivateKey privateKey = loadPrivateKey(privateKeyIs,null);

        return getKeyStorePasswordPair(certChain, privateKey);
    }

    private static PrivateKey loadPrivateKey(final InputStream fileIs, final String algorithm) {
        PrivateKey privateKey = null;

        try (DataInputStream stream = new DataInputStream(fileIs)) {
            privateKey = PrivateKeyReader.getPrivateKey(stream, algorithm);
        } catch (IOException | GeneralSecurityException e) {
            System.out.println("Failed to load private key from file");
        }

        return privateKey;
    }

    public static KeyStorePasswordPair getKeyStorePasswordPair(final List<Certificate> certificates, final PrivateKey privateKey) {
        KeyStore keyStore;
        String keyPassword;
        try {
            keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null);

            // randomly generated key password for the key in the KeyStore
            keyPassword = new BigInteger(128, new SecureRandom()).toString(32);

            Certificate[] certChain = new Certificate[certificates.size()];
            certChain = certificates.toArray(certChain);
            keyStore.setKeyEntry("alias", privateKey, keyPassword.toCharArray(), certChain);
        } catch (KeyStoreException | NoSuchAlgorithmException | CertificateException | IOException e) {
            System.out.println("Failed to create key store");
            return null;
        }

        return new KeyStorePasswordPair(keyStore, keyPassword);
    }

    public void connect(Context context) {
        FptTtsClient ttsClient = new FptTtsClient(context);

        String clientEndpoint = "arpfcnmms8u2o-ats.iot.ap-southeast-1.amazonaws.com";
        String clientId = "sdk-java";

        try {
            InputStream certificate = inputStreamFromAssetFile(context, "certs/aobot_001.cert.pem");
            InputStream privateKey = inputStreamFromAssetFile(context, "certs/aobot_001.private.key");

            KeyStorePasswordPair pair = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                pair = getKeyStorePasswordPair(certificate, privateKey);
            }

            AWSIotMqttClient client = new AWSIotMqttClient(clientEndpoint, clientId, pair.keyStore, pair.keyPassword);
            client.connect();

            String topicName = "sdk/test/java";
            AWSIotQos qos = AWSIotQos.QOS0;

            MqttRobotTopic topic = new MqttRobotTopic(topicName, qos, this.robotManager, ttsClient);
            client.subscribe(topic);
        } catch (Exception e) {
            Log.e("Aobot","Error when connect with AWS IoT");
            throw new RuntimeException(e);
        }
    }
}