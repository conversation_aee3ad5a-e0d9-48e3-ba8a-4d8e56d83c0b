package com.vetc.aobot.service;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.nio.ShortBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtEnvironment;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;

public class WhisperSpeechRecognition {
    private static final String TAG = "WhisperSpeech";
    private static final int SAMPLE_RATE = 16000;
    private static final int N_FFT = 400;
    private static final int N_MELS = 80;
    private static final int HOP_LENGTH = 160;
    private static final int CHUNK_LENGTH = 30; // seconds
    private static final int ENCODER_DIM = 512;
    
    private Context context;
    private OrtEnvironment env;
    private OrtSession encoderSession;
    private OrtSession decoderSession;

    private Map<Integer, String> tokenMap = new HashMap<>();
    @RequiresApi(api = Build.VERSION_CODES.N)
    public WhisperSpeechRecognition(Context context) {
        loadTokenMap(context);
        this.context = context;
        Log.d(TAG, "Whisper model loading...");

        try {
            // Khởi tạo ONNX Runtime environment
            env = OrtEnvironment.getEnvironment();
            
            // Tạo thư mục asr trong filesDir nếu chưa tồn tại
            File asrDir = new File(context.getFilesDir(), "asr");
            if (!asrDir.exists()) {
                asrDir.mkdirs();
            }

            // Đường dẫn đến file mô hình
            File encoderFile = new File(asrDir, "whisper_encoder.onnx");
            File decoderFile = new File(asrDir, "whisper_decoder.onnx");

            // Sao chép mô hình từ assets nếu chưa tồn tại
            if (!encoderFile.exists()) {
                copyAssetToFile(context, "asr/whisper_encoder.onnx", encoderFile);
            }

            if (!decoderFile.exists()) {
                copyAssetToFile(context, "asr/whisper_decoder.onnx", decoderFile);
            }
            
            // Kiểm tra xem file có tồn tại không
            if (!encoderFile.exists() || !decoderFile.exists()) {
                Log.e(TAG, "Model files not found. Please copy models to app files directory.");
                throw new RuntimeException("Model files not found. Please copy models to app files directory.");
            }
            
            // Tạo session cho encoder và decoder
            encoderSession = env.createSession(encoderFile.getAbsolutePath());
            decoderSession = env.createSession(decoderFile.getAbsolutePath());

            // In ra thông tin về đầu vào và đầu ra của mô hình
            logModelInfo(encoderSession, "Encoder");
            logModelInfo(decoderSession, "Decoder");

            Log.d(TAG, "Whisper model loaded successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Whisper model", e);
            throw new RuntimeException("Error initializing Whisper model: " + e.getMessage(), e);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private void loadTokenMap(Context context) {
        try {
            InputStream is = context.getAssets().open("asr/vocab.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();

            String json = new String(buffer, StandardCharsets.UTF_8);
            JSONObject obj = new JSONObject(json);

            // Đọc từng key-value trong JSON
            Iterator<String> keys = obj.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                int id = obj.getInt(key);
                tokenMap.put(id, key);
            }

            Log.d(TAG, "Token 50257 -> " + tokenToText(50257)); // <|startoftranscript|>
            Log.d(TAG, "Token 499 -> " + tokenToText(499));     // "xin"
            Log.d(TAG, "Token 347 -> " + tokenToText(347));     // "chào"
            Log.d(TAG, "✅ Token map loaded with " + tokenMap.size() + " tokens");
        } catch (JSONException | IOException e) {
            Log.e(TAG, "❌ Lỗi khi đọc vocab.json", e);
        }
    }

    private void copyAssetToFile(Context context, String assetPath, File destFile) throws IOException {
        try (InputStream in = context.getAssets().open(assetPath);
             OutputStream out = new FileOutputStream(destFile)) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
        }
    }

    private float[] loadAudioFile(File audioFile) throws IOException {
        // Đọc file âm thanh và chuyển đổi thành mảng float
        // (Cụ thể cách đọc file âm thanh phụ thuộc vào định dạng file)
        // Trong ví dụ này, chúng ta giả định rằng file âm thanh là PCM 16-bit mono
        FileInputStream fis = new FileInputStream(audioFile);
        byte[] audioBytes = new byte[(int) audioFile.length()];
        fis.read(audioBytes);
        fis.close();

        ShortBuffer shortBuffer = ByteBuffer.wrap(audioBytes).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer();
        float[] audioData = new float[shortBuffer.limit()];
        for (int i = 0; i < shortBuffer.limit(); i++) {
            audioData[i] = shortBuffer.get(i) / 32768.0f;
        }

        return audioData;
    }

    private float[][] computeLogMelSpectrogram(float[] audioData) {
        // Whisper mong đợi đầu vào có độ dài cố định là 3000 frames
        int expectedFrames = 3000;

        // Tạo một mel spectrogram trống với kích thước cố định
        float[][] melSpectrogram = new float[N_MELS][expectedFrames];

        // Tính toán mel spectrogram thực tế (giả lập)
        // Trong thực tế, bạn sẽ sử dụng thư viện xử lý tín hiệu để tính toán
        int actualFrames = audioData.length / HOP_LENGTH;

        // Đảm bảo không vượt quá kích thước mong đợi
        int framesToUse = Math.min(actualFrames, expectedFrames);

        // Điền dữ liệu vào mel spectrogram
        for (int i = 0; i < N_MELS; i++) {
            for (int j = 0; j < framesToUse; j++) {
                // Giả lập tính toán mel spectrogram
                // Trong thực tế, bạn sẽ sử dụng giá trị thực từ phép biến đổi mel
                melSpectrogram[i][j] = (float) Math.log(1e-10 + Math.abs(audioData[j * HOP_LENGTH] * 0.5));
            }

            // Điền 0 cho phần còn lại nếu cần
            for (int j = framesToUse; j < expectedFrames; j++) {
                melSpectrogram[i][j] = 0.0f;
            }
        }

        return melSpectrogram;
    }

    private int argmax(float[] array) {
        int maxIndex = 0;
        for (int i = 1; i < array.length; i++) {
            if (array[i] > array[maxIndex]) {
                maxIndex = i;
            }
        }
        return maxIndex;
    }

    private long[] appendToken(long[] tokens, int newToken) {
        long[] newTokens = Arrays.copyOf(tokens, tokens.length + 1);
        newTokens[tokens.length] = newToken;
        return newTokens;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private String tokenToText(int token) {
        return tokenMap.getOrDefault(token, "");
    }

    /**
     * Kiểm tra xem mô hình đã được tải đúng chưa
     * @return true nếu cả encoder và decoder đều đã được tải
     */
    public boolean isModelLoaded() {
        return encoderSession != null && decoderSession != null;
    }

    /**
     * Interface callback để nhận kết quả nhận dạng
     */
    public interface TranscriptionCallback {
        void onTranscriptionComplete(String transcript);
    }

    /**
     * Thực hiện nhận dạng âm thanh từ file
     * @param audioFile File âm thanh cần nhận dạng
     * @param callback Callback để nhận kết quả
     */
    public void transcribeAudio(File audioFile, TranscriptionCallback callback) {
        if (!isModelLoaded()) {
            callback.onTranscriptionComplete("Error: Model not loaded properly");
            return;
        }

        // Thực hiện nhận dạng trong background
        new TranscriptionTask().execute(audioFile);
    }

    /**
     * AsyncTask để thực hiện nhận dạng trong background
     */
    public class TranscriptionTask extends AsyncTask<File, Void, String> {
        @Override
        protected String doInBackground(File... files) {
            if (files.length == 0 || files[0] == null) {
                return "No audio file provided";
            }

            File audioFile = files[0];
            try {
                // Đọc file âm thanh và chuyển đổi thành mảng float
                float[] audioData = loadAudioFile(audioFile);

                // Tính toán log mel spectrogram
                float[][] melSpectrogram = computeLogMelSpectrogram(audioData);

                // Chuyển đổi thành tensor đầu vào cho encoder
                // Thay đổi từ 4 chiều [1][1][N_MELS][time] thành 3 chiều [1][N_MELS][time]
                float[][][] encoderInput = new float[1][N_MELS][melSpectrogram[0].length];
                for (int i = 0; i < N_MELS; i++) {
                    for (int j = 0; j < melSpectrogram[0].length; j++) {
                        encoderInput[0][i][j] = melSpectrogram[i][j];
                    }
                }

                // Chạy encoder
                OnnxTensor inputTensor = OnnxTensor.createTensor(env, encoderInput);
                Map<String, OnnxTensor> inputs = new HashMap<>();
                inputs.put("input", inputTensor);

                // Khai báo biến audioFeatures ở ngoài khối try-catch
                float[][][] audioFeatures;

                try {
                    OrtSession.Result encoderOutput = encoderSession.run(inputs);
                    // Lấy kết quả từ encoder
                    audioFeatures = (float[][][]) encoderOutput.get(0).getValue();
                } catch (OrtException e) {
                    // Nếu gặp lỗi, thử lại với tên đầu vào khác
                    if (e.getMessage().contains("Unknown input name")) {
                        inputs.clear();
                        inputs.put("mel", inputTensor); // Thử với tên "mel"
                        OrtSession.Result encoderOutput = encoderSession.run(inputs);
                        audioFeatures = (float[][][]) encoderOutput.get(0).getValue();
                    } else {
                        throw e; // Ném lại ngoại lệ nếu không phải lỗi tên đầu vào
                    }
                }

                // Khởi tạo decoder với token bắt đầu
                long[] tokens = new long[1]; // Bắt đầu với token <|startoftranscript|>
                StringBuilder transcript = new StringBuilder();

                for (int i = 0; i < 100; i++) { // Giới hạn số lượng token
                    // Chỉ lấy token cuối cùng
                    long[] lastToken = new long[]{tokens[tokens.length - 1]};
                    long[][] tokenArray = new long[1][1];
                    tokenArray[0][0] = lastToken[0];

                    OnnxTensor tokenTensor = OnnxTensor.createTensor(env, tokenArray);
                    OnnxTensor featuresTensor = OnnxTensor.createTensor(env, audioFeatures);

                    Map<String, OnnxTensor> decoderInputs = new HashMap<>();
                    decoderInputs.put("input_ids", tokenTensor);
                    decoderInputs.put("encoder_hidden_states", featuresTensor);

                    try {
                        OrtSession.Result decoderOutput = decoderSession.run(decoderInputs);
                        Object outputObj = decoderOutput.get(0).getValue();
                        float[][] logits;
                        if (outputObj instanceof float[][][]) {
                            logits = ((float[][][]) outputObj)[0];
                        } else if (outputObj instanceof float[][]) {
                            logits = (float[][]) outputObj;
                        } else {
                            Log.e(TAG, "Unexpected output type");
                            break;
                        }

                        int nextToken = argmax(logits[0]);
                        if (nextToken == 50257) break; // Kết thúc nếu gặp EOT token

                        tokens = appendToken(tokens, nextToken);
                        String tokenText = null;
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            tokenText = tokenToText(nextToken);
                        }
                        transcript.append(tokenText);

                        tokenTensor.close();
                        featuresTensor.close();

                    } catch (OrtException e) {
                        Log.e(TAG, "Decoder error: " + e.getMessage());
                        break;
                    }
                }

                // Giải phóng tài nguyên
                inputTensor.close();

                return transcript.toString();
            } catch (Exception e) {
                Log.e(TAG, "Error during transcription", e);
                return "Error: " + e.getMessage();
            }
        }
    }

    /**
     * Giải phóng tài nguyên khi không cần thiết nữa
     */
    public void close() {
        try {
            if (encoderSession != null) {
                encoderSession.close();
                encoderSession = null;
            }
            if (decoderSession != null) {
                decoderSession.close();
                decoderSession = null;
            }
            if (env != null) {
                env.close();
                env = null;
            }
        } catch (OrtException e) {
            Log.e(TAG, "Error closing ONNX Runtime resources", e);
        }
    }

    private void logModelInfo(OrtSession session, String modelName) {
        try {
            // In ra thông tin về đầu vào
            Log.d(TAG, modelName + " inputs:");
            for (String inputName : session.getInputNames()) {
                Log.d(TAG, "  - " + inputName);
            }

            // In ra thông tin về đầu ra
            Log.d(TAG, modelName + " outputs:");
            for (String outputName : session.getOutputNames()) {
                Log.d(TAG, "  - " + outputName);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting model info", e);
        }
    }
}
