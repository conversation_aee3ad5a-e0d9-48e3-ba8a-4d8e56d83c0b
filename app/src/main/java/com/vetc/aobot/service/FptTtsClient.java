package com.vetc.aobot.service;

import android.content.Context;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.vetc.aobot.MainActivity;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class FptTtsClient {
    private MediaPlayer mediaPlayer;
    private Context context;
    private OkHttpClient client = new OkHttpClient();
    private String apiKey = "6cptEJTtCLDp2QvmpZPcqrrJh2bM4Pjy";

    public FptTtsClient(Context context ) {
        this.context = context;
    }

    // Hàm gọi FPT AI TTS API
    public void convertTextToSpeech(String text) {
//        RequestBody formBody = new FormBody.Builder()
//                .add("content", text)
//                .build();
        RequestBody requestBody = RequestBody.create(text, MediaType.get("text/plain; charset=utf-8"));

        Request request = new Request.Builder()
                .url("https://api.fpt.ai/hmi/tts/v5")
                .post(requestBody)
                .addHeader("api-key", apiKey)
                .addHeader("voice", "banmai")
                .addHeader("speed", "")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject json = new JSONObject(responseBody);
                if (json.getInt("error") == 0) {
                    String audioUrl = json.getString("async");

                    NetworkUtils.postData("TTS: Đã nhận được URL âm thanh: " + audioUrl);
                    playAudioFromUrl(audioUrl);
//                    downloadAndPlayAudio(audioUrl, context);
                }
            } else {
                Toast.makeText(context, "Lỗi từ FPT TTS", Toast.LENGTH_SHORT).show();
            }
        } catch (IOException | JSONException e) {
            Toast.makeText(context,"Không thể kết nối tới FPT TTS API", Toast.LENGTH_SHORT).show();
        }
    }

    private void downloadAndPlayAudio(String audioUrl, Context context) {
        Request request = new Request.Builder()
                .url(audioUrl)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                NetworkUtils.postData("TTS: Không thể tải file âm thanh");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful() && response.body() != null) {
                    NetworkUtils.postData("TTS: Đã tải file âm thanh");
                    File outputDir = context.getCacheDir();
                    File outputFile = new File(outputDir, "output_audio.mp3");

                    try (InputStream is = response.body().byteStream();
                         FileOutputStream fos = new FileOutputStream(outputFile)) {

                        byte[] buffer = new byte[1024];
                        int read;

                        while ((read = is.read(buffer)) != -1) {
                            fos.write(buffer, 0, read);
                        }

                        fos.flush();
                    } catch (IOException e) {
                        NetworkUtils.postData("TTS: Lỗi khi lưu file");
                    }
                    NetworkUtils.postData("TTS: Đã lưu file âm thanh");

//                    playAudioFromPath(outputFile.getAbsolutePath());
                }
            }
        });
    }

    private void playAudioFromUrl(String audioUrl) {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }

            mediaPlayer = new MediaPlayer();

            // Thêm header User-Agent để tránh bị chặn bởi server
            Map<String, String> headers = new HashMap<>();
            headers.put("User-Agent", "Android");

            // Dùng phương thức setDataSource(Context, Uri, Map<String, String>)
            mediaPlayer.setDataSource(context, Uri.parse(audioUrl), headers);
            mediaPlayer.prepareAsync();

            mediaPlayer.setOnPreparedListener(mp -> {
                Log.d("TTS", "Đã sẵn sàng phát");
                mediaPlayer.start();
            });

            mediaPlayer.setOnCompletionListener(mp -> {
                Log.d("TTS", "Phát xong");
                mediaPlayer.release();
                mediaPlayer = null;
            });

        } catch (IOException e) {
            Log.e("MediaPlayer", "Không thể chuẩn bị phát âm thanh từ URL", e);
            Toast.makeText(context, "Lỗi phát âm thanh từ server", Toast.LENGTH_SHORT).show();
        }
    }
//    private void playAudioFromPath(String path) {
//        try {
//            NetworkUtils.postData("TTS: Đang phát file âm thanh" + path);
//            mediaPlayer = MediaPlayer.create(context, Uri.fromFile(new File(path)));
//
//            if (mediaPlayer != null) {
//                mediaPlayer.release();
//                mediaPlayer = null;
//            }
//
//            mediaPlayer = new MediaPlayer();
//            mediaPlayer.setDataSource(path);
//            mediaPlayer.prepareAsync();
//            mediaPlayer.setOnPreparedListener(mp -> {
//                mediaPlayer.start();
//            });
//
//            mediaPlayer.setOnCompletionListener(mp -> {
//                mediaPlayer.release();
//            });
//
//        } catch (IOException e) {
//            Log.e("TTS", "Không thể phát file âm thanh", e);
//        }
//    }
}