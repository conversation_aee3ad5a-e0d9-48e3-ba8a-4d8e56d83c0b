package com.vetc.aobot.service;

import android.util.Log;
import android.os.AsyncTask;

import com.amazonaws.services.iot.client.AWSIotMessage;
import com.amazonaws.services.iot.client.AWSIotQos;
import com.amazonaws.services.iot.client.AWSIotTopic;
import com.aobo.aoborobotsdk.platform.AoboRobotManeger;
import com.slamtec.slamware.action.IMoveAction;
import com.slamtec.slamware.robot.Pose;

import org.json.JSONObject;

public class MqttRobotTopic extends AWSIotTopic{
    AoboRobotManeger robotManager;
    FptTtsClient fptTtsClient;
    public MqttRobotTopic(String topic, AWSIotQos qos, AoboRobotManeger rm, FptTtsClient fptTtsClient) {
        super(topic, qos);
        this.robotManager = rm;
        this.fptTtsClient = fptTtsClient;
    }

    @Override
    public void onMessage(AWSIotMessage message) {
        NetworkUtils.postData(message.getStringPayload());

        try {
            JSONObject json = new JSONObject(message.getStringPayload());
            if (json.has("action") && json.getString("action").equals("goto")) {
                float x = (float) json.getDouble("x");
                float y = (float) json.getDouble("y");
                float yaw = (float) json.getDouble("yaw");

                // Gọi phương thức async thay vì phương thức đồng bộ
                moveToTargetAsync(x, y, yaw);

                String text = json.getString("text");
                if (!text.isEmpty())
                    fptTtsClient.convertTextToSpeech(text);
            }

            if (json.has("action") && json.getString("action").equals("speech")) {
                fptTtsClient.convertTextToSpeech(json.getString("text"));
            }
        } catch (Exception e) {
            Log.e("MQTT", "Không thể parse JSON", e);
        }
    }

    private void moveToTargetAsync(float x, float y, float yaw) {
        NetworkUtils.postData("Chuẩn bị di chuyển đến [X:" + x + ",Y:" + y + ",Yaw:" + yaw + "]");
        new MoveToTargetTask().execute(new Pose(x, y, 0.0f, yaw, 0.0f, 0.0f));
    }

    // AsyncTask để thực hiện di chuyển robot trong background
    private class MoveToTargetTask extends AsyncTask<Pose, Void, Boolean> {
        @Override
        protected void onPreExecute() {
            NetworkUtils.postData("Bắt đầu di chuyển robot...");
        }

        @Override
        protected Boolean doInBackground(Pose... poses) {
            if (poses.length == 0) return false;

            Pose targetPose = poses[0];
            NetworkUtils.postData("Tôi đi đây [X:" + targetPose.getX() + ",Y:" + targetPose.getY() + ",Yaw:" + targetPose.getYaw() + "]");

            IMoveAction iMoveAction = robotManager.moveToPoint(targetPose, "1.stcm", false, false);

            if (iMoveAction != null) {
                NetworkUtils.postData("onPostExecute-waitUntilDone = ");
                try {
                    iMoveAction.waitUntilDone();
                    NetworkUtils.requestSpeech("Tôi đã đến đích rồi");
                    return true;
                } catch (Exception e) {
                    NetworkUtils.postData("Lỗi khi di chuyển: " + e.getMessage());
                    return false;
                }
            } else {
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            if (success) {
                NetworkUtils.postData("onPostExecute-waitUntilDone = Thực hiện hoàn tất");
            } else {
                NetworkUtils.postData("onPostExecute - Lỗi khi di chuyển robot");
            }
        }
    }

    private void moveToTarget(float x, float y, float yaw) {
        NetworkUtils.postData("Tôi đi đây [X:" + x + ",Y:" + y + ",Yaw:" + yaw + "]");
        Pose targetPose = new Pose(x, y, 0.0f, yaw, 0.0f, 0.0f);
        IMoveAction iMoveAction = robotManager.moveToPoint(targetPose, "1.stcm", false, false);

        if (iMoveAction != null) {
            NetworkUtils.postData("onPostExecute-waitUntilDone = ");
            iMoveAction.waitUntilDone();
            NetworkUtils.postData("onPostExecute-waitUntilDone = Execution completed");
        } else {
            NetworkUtils.postData("onPostExecute Fatal error");
        }
    }
}
