package com.vetc.aobot.service;

import static androidx.core.content.ContextCompat.getSystemService;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioDeviceInfo;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.MicrophoneInfo;
import android.os.Build;
import android.util.Log;
import android.util.Pair;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AudioCapabilityChecker {

    private static final String TAG = "AudioCapabilityChecker";

    public static void checkSupportedConfigs() {
        NetworkUtils.postData("Bắt đầu kiểm tra các cấu hình âm thanh khả dụng...");

        // <PERSON>ác tốc độ lấy mẫu phổ biến cần kiểm tra
        int[] sampleRates = {
                8000,
                11025,
                16000,
                22050,
                32000,
                44100,
                48000
        };

        // <PERSON><PERSON><PERSON> cấu hình kênh âm thanh
        int[] channelConfigs = {
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.CHANNEL_IN_STEREO
        };

        // Các định dạng âm thanh
        int[] audioFormats = {
                AudioFormat.ENCODING_PCM_16BIT,
                AudioFormat.ENCODING_PCM_8BIT
        };

        for (int rate : sampleRates) {
            for (int channel : channelConfigs) {
                for (int format : audioFormats) {
                    int bufferSize = AudioRecord.getMinBufferSize(rate, channel, format);

                    boolean isSupported = (bufferSize != AudioRecord.ERROR_BAD_VALUE &&
                            bufferSize != AudioRecord.ERROR_INVALID_OPERATION);

                    if (isSupported) {
                        String channelDesc = (channel == AudioFormat.CHANNEL_IN_MONO) ? "Mono" : "Stereo";
                        String formatDesc = (format == AudioFormat.ENCODING_PCM_16BIT) ? "PCM 16-bit" : "PCM 8-bit";

                        NetworkUtils.postData(String.format("✅ Hỗ trợ: Sample Rate=%dHz | %s | %s",
                                rate, channelDesc, formatDesc));
                    }
                }
            }
        }

        NetworkUtils.postData( "Kết thúc kiểm tra cấu hình âm thanh.");
    }

    @SuppressLint("NewApi")
    public static void getAvaiableMicrophone(Context context) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        List<MicrophoneInfo> microphones = new ArrayList<>();
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                microphones = audioManager.getMicrophones();
                for (MicrophoneInfo microphone : microphones) {
                    List<Pair<Integer, Integer>> channelMapping = microphone.getChannelMapping();
                    String channelMappingString = "";
                    for (Pair<Integer, Integer> pair : channelMapping) {
                        Log.d(TAG, "Channel mapping: " + pair.first + " -> " + pair.second);
                        NetworkUtils.postData("Channel mapping: " + pair.first + " -> " + pair.second);
                        channelMappingString += pair.first + " -> " + pair.second + ", ";
                    }
                    NetworkUtils.postData("MicArray: Microphone " +
                            "- description [" + microphone.getDescription() + "] " +
                            "- location [" + microphone.getLocation() + "] " +
                            "- Address [" + microphone.getAddress() + "]" +
                            "- group [" + microphone.getGroup() + "]" +
                            "- type [" + microphone.getType() + "]" +
                            "- index [" + channelMappingString + "]" +
                            "- Directionality [" + microphone.getDirectionality() + "]");
                }
            }
        } catch (Exception e) {
            Log.e("MicArray", "Error getting mic info: ", e);
        }

//        for (AudioDeviceInfo device : audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)) {
//            NetworkUtils.postData("MicArray: Device ID: " + device.getId() +
//                    " - Device type: " + device.getType() +
//                    " - Channels: " + device.getChannelCounts() +
//                    " - Formats: " + device.getEncodings());
//            Log.d("MicArray", "Device ID: " + device.getId());
//            Log.d("MicArray", "Device type: " + device.getType());
//            Log.d("MicArray", "Channels: " + device.getChannelCounts().length);
//            Log.d("MicArray", "Formats: " + device.getEncodings().length);
//        }
        for (AudioDeviceInfo device : audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)) {
            Log.d("MicArray", "Device ID: " + device.getId());
            Log.d("MicArray", "Type: " + device.getType());
            Log.d("MicArray", "Address: " + device.getAddress());
            Log.d("MicArray", "Channels: " + Arrays.toString(device.getChannelCounts()));
            Log.d("MicArray", "Formats: " + Arrays.toString(device.getEncodings()));
        }
    }
}