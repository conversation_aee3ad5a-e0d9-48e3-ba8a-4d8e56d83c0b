package com.vetc.aobot.service;

import android.util.Log;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class NetworkUtils {
    private static final String LOG_SERVER = "https://nodered.evercharge.vn/vetc/logging";
    private static final String SPEECH_SERVER = "https://grsdebug.evercharge.vn/iot/speech";
    private static final OkHttpClient client = new OkHttpClient();
    public static void postData(String content) {
        Log.d(NetworkUtils.class.getName(),content);
        MediaType JSON = MediaType.get("application/json; charset=utf-8");

        JSONObject json = new JSONObject();
        try {
            Date currentTime = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String timeString = sdf.format(currentTime);

            json.put("log", content);
            json.put("time", timeString);
        } catch (JSONException e) {}

        RequestBody body = RequestBody.create(json.toString(), JSON);

        Request request = new Request.Builder()
                .url(LOG_SERVER)
                .post(body)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                String result = response.body().string();
                Log.i(NetworkUtils.class.getName(),result);
            }

            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.i(NetworkUtils.class.getName(), "Send Log to Server FAIL");
            }
        });
    }

    /**
     * Gửi yêu cầu phát âm thanh đến robot
     * @param text Nội dung cần phát âm
     */
    public static void requestSpeech(String text) {
        Log.d(NetworkUtils.class.getName(), "Yêu cầu phát âm: " + text);
        MediaType JSON = MediaType.get("application/json; charset=utf-8");

        JSONObject json = new JSONObject();
        try {
            json.put("text", text);
        } catch (JSONException e) {
            Log.e(NetworkUtils.class.getName(), "Lỗi tạo JSON", e);
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);

        Request request = new Request.Builder()
                .url(SPEECH_SERVER)
                .post(body)
                .header("Content-Type", "application/json")
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                String result = response.body().string();
                Log.i(NetworkUtils.class.getName(), "Kết quả yêu cầu phát âm: " + result);
                postData("Đã gửi yêu cầu phát âm: " + text);
            }

            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.e(NetworkUtils.class.getName(), "Gửi yêu cầu phát âm thất bại", e);
                postData("Gửi yêu cầu phát âm thất bại: " + e.getMessage());
            }
        });
    }
}