package com.vetc.aobot.utils;

import android.content.Context;

import java.io.InputStream;

public class FileUtils {
    public static String getAssetsContent(Context context, String path) {
        int len = 0;
        byte[] buf = null;
        String result = "";
        try {
            InputStream in = context.getAssets().open(path);
            len = in.available();
            buf = new byte[len];
            in.read(buf, 0, len);

            result = new String(buf);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
