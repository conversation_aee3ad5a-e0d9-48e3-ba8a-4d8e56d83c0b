package com.vetc.aobot;

import android.annotation.SuppressLint;
import android.graphics.Point;
import android.os.AsyncTask;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.aobo.aoborobotsdk.events.ConnectedEvent;
import com.aobo.aoborobotsdk.events.ConnectionFailedEvent;
import com.aobo.aoborobotsdk.events.ConnectionLostEvent;
import com.aobo.aoborobotsdk.events.GetUpdateMapStatusEvent;
import com.aobo.aoborobotsdk.events.LaserScanUpdateEvent;
import com.aobo.aoborobotsdk.events.LineUpdateEvent;
import com.aobo.aoborobotsdk.events.MapUpdateEvent;
import com.aobo.aoborobotsdk.events.MoveActionUpdateEvent;
import com.aobo.aoborobotsdk.events.RobotHealthInfoEvent;
import com.aobo.aoborobotsdk.events.RobotPoseUpdateEvent;
import com.aobo.aoborobotsdk.events.RobotStatusUpdateEvent;
import com.aobo.aoborobotsdk.events.WallUpdateEvent;
import com.aobo.aoborobotsdk.impl.AoboNaviListener;
import com.aobo.aoborobotsdk.platform.AoboRobotManeger;
import com.aobo.aoborobotsdk.utils.LogUtil;
import com.aobo.aoborobotsdk.utils.RPGestureDetector;
import com.aobo.aoborobotsdk.views.controls.RPControlBar;
import com.aobo.aoborobotsdk.views.controls.RPMapView;
import com.aobo.aoborobotsdk.views.controls.RPMoveControlPanel;
import com.aobo.aoborobotsdk.views.controls.RPVirtualWallEditPanel;
import com.slamtec.slamware.action.IMoveAction;
import com.slamtec.slamware.action.MoveDirection;
import com.slamtec.slamware.message.DepthCameraFrame;
import com.slamtec.slamware.robot.Pose;
import com.vetc.aobot.service.NetworkUtils;

public class MapActivity extends AppCompatActivity  implements RPGestureDetector.OnRPGestureListener,AoboNaviListener {
    public static String TAG = "MapActivity";
    AoboRobotManeger aoboRobotManeger = AoboRobotManeger.getInstance();

    private RPMapView mapView;
    private RPGestureDetector gestureDetector;
    private static int statusBarHeight = 0;
    // wall edit mode
    private boolean isWallEditMode = false;
    private Float mapScale = 2.0f;

    // wall edit add or remove
    private int wallEditMode = MODE_WALL_NONE;
    // wall edit mode
    private final static int MODE_WALL_NONE = 0;
    private final static int MODE_WALL_ADD = 1;
    private final static int MODE_WALL_REMOVE = 2;
    private final static float kRPMapViewMinScale = 0.5f;
    private final static float kRPMapViewMaxScale = 16.0f;

    // sweep spot mode
    private boolean isSweepSpot = false;
    ImageButton btnStop;
    Button buttonGopoint;
    private TextView textStatus;
    TextView tvSdk,tvBattery,tvConnect,tvQuality,tvRobotPose,tv_NaviStatus;
    String mapUpdataTip;
    String localStatusTip;
    long upDataTime = System.currentTimeMillis();
    private RPControlBar controlBar;
    private RelativeLayout root;
    private RPMoveControlPanel controlPanel;
    private RPVirtualWallEditPanel virtualWallEditPanel;
    private DepthCameraFrame depthCameraFrame = new DepthCameraFrame();
    int countNaviTime = 0;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_map);
        mapView = (RPMapView) findViewById(R.id.map_view);
        gestureDetector = new RPGestureDetector(this);
        initView();

        depthCameraFrame = new DepthCameraFrame();
        depthCameraFrame.setMinFovYaw(-33.0f * 3.14f / 180);
        depthCameraFrame.setMaxFovYaw(33.0f * 3.14f / 180);
        depthCameraFrame.setMinFovPitch(-22f * 3.14f / 180);
        depthCameraFrame.setMaxFovPitch(22f * 3.14f / 180);
        depthCameraFrame.setCols(R.color.blue);
        depthCameraFrame.setRows(2);
    }
    @SuppressLint("WrongViewCast")
    public  void initView(){
        btnStop = findViewById(R.id.button_stop);
        btnStop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tv_NaviStatus.setText("cancelAction");
                aoboRobotManeger.cancelAction();
            }
        });
        buttonGopoint = findViewById(R.id.button_gopoint);
        buttonGopoint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                countNaviTime++;
                Pose goPose;
                if (countNaviTime % 2 == 1){
                    Pose getPoseStart = new Pose();
                    getPoseStart.setX(-3.08f);
                    getPoseStart.setY(19.71f);
                    getPoseStart.setYaw(2.55f);
                    goPose = getPoseStart;
                }else{
                    Pose getPoseEnd = new Pose();
                    getPoseEnd.setX(-1.06f);
                    getPoseEnd.setY(-14.3f);
                    getPoseEnd.setYaw(-0.90f);
                    goPose = getPoseEnd;
                }
                NetworkUtils.postData("onClick: countNaviTime="+countNaviTime+",goPose = "+goPose.getX()+", y= "+goPose.getY());


                new ChangeAnsyncTask().execute(goPose);
            }
        });
        tvSdk = findViewById(R.id.text_sdp_version_content);
        tvBattery = findViewById(R.id.text_battery_percentage_content);
        tvConnect = findViewById(R.id.text_connection_status_content);
        tvQuality = findViewById(R.id.text_localization_quality);
        textStatus = findViewById(R.id.text_status);
        tvRobotPose = findViewById(R.id.text_localization_robot_pose);
        tv_NaviStatus = findViewById(R.id.text_robot_navi_status);
        controlBar = (RPControlBar) findViewById(R.id.control_bar);
        root = (RelativeLayout) findViewById(R.id.root);
        controlBar.setClickListener(new RPControlBar.IOnButtonClickListener() {
            @Override
            public void onButtonEditWallClicked() {
                textStatus.setText(getString(R.string.edit_wall));
                isWallEditMode = true;
                wallEditMode = MODE_WALL_NONE;
                showVirtualWallEditPanel();
            }

            @Override
            public void onButtonClearMapClicked() {
                aoboRobotManeger.clearMap();
            }

            @Override
            public void onButtonSaveMapClicked() {
                String mapPath = "/sdcard/map";
                String mapName = "map.stcm";
                aoboRobotManeger.saveMap(mapPath,mapName);
            }

            @Override
            public void onButtonHomeClicked() {
                aoboRobotManeger.goHome();
            }

            @Override
            public void onButtonClearTrackClicked() {

            }

            @Override
            public void onButtonQuitToConnectClicked() {
                goBackToMainActivity();
            }

            @Override
            public void onButtonControllerClicked() {
                if (isWallEditMode) {
                    return;
                }
                showMoveControlPanel();
            }

            @Override
            public void onButtonLoadMapClicked() {
                String mapPath = "/sdcard/map";
                String mapName = "map.stcm";
                if (aoboRobotManeger.isConnectedToRobot()){
                    aoboRobotManeger.loadMap(mapPath,mapName);
                }else{
                    LogUtil.d(TAG,"机器人断开连接");

                }


            }
        });
    }
    @Override
    protected void onResume() {
        LogUtil.d(TAG,"onResume");
        aoboRobotManeger.stopGetMap(true);
        aoboRobotManeger.setAutoUpdateMap(true);
        aoboRobotManeger.getMap(this, mapView);
        aoboRobotManeger.setOnAoboNaviListener(this);

        super.onResume();
    }
    @Override
    protected void onStart() {
        super.onStart();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        aoboRobotManeger.disConnect();

    }
    @Override
    protected void onStop() {

        super.onStop();
    }
    @Override
    protected void onPause() {
        aoboRobotManeger.stopGetMap(false);
        super.onPause();
    }

    public enum ActionStatus {
        WAITING_FOR_START, //动作已经创建，等待机器开始
        RUNNING,// 机器正在运行中
        FINISHED,//机器动作已经完成
        PAUSED,// 机器动作暂停
        STOPPED,//动作停止
        ERROR;//遇到错误

        private ActionStatus() {
        }
    }

    @Override
    public void onMapTap(MotionEvent event) {
        // 地图点击
        Log.d(TAG, "onMapTap: "+event.getX());
        int x = Math.round(event.getX());
        int y = Math.round(event.getY()) - statusBarHeight;
        Point rawPoint = new Point(x, y);
        if (isWallEditMode) {
            if (wallEditMode == MODE_WALL_ADD) {
                mapView.setVirtualWallIndicator(rawPoint);
            } else if (wallEditMode == MODE_WALL_REMOVE) {
                mapView.removeWall(rawPoint);
                wallEditMode = MODE_WALL_NONE;
            }
        } else {
            if (isSweepSpot) {
                mapView.sweepSpot(rawPoint);
                isSweepSpot = false;

            } else {
                mapView.moveTo(rawPoint);
            }
        }
    }

    @Override
    public void onMapPinch(float factor) {
        // 地图缩放
        mapScale *= factor;
        if (mapScale < kRPMapViewMinScale) {
            mapScale = kRPMapViewMinScale;
        } else if (mapScale > kRPMapViewMaxScale) {
            mapScale = kRPMapViewMaxScale;
        }
        mapView.setMapScale(mapScale, false);
    }

    @Override
    public void onMapMove(int distanceX, int distanceY) {
        mapView.setMapTransition(new Point(Math.round(distanceX), Math.round(distanceY)));
    }

    @Override
    public void onMapRotate(float factor) {
        mapView.setMapRotation(factor);
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mapScale = mapView.getMapScale();
                break;
        }
        gestureDetector.onTouchEvent(event);
        return super.onTouchEvent(event);
    }

    @Override
    public void getUpdateMapStatus(GetUpdateMapStatusEvent getUpdateMapStatusEvent) {

        Log.d(TAG, "onEventMainThread: isMapStatusLocation>>" + getUpdateMapStatusEvent.isMapStatusLocation());
    }

    @Override
    public void getRobotHealthInfo(RobotHealthInfoEvent robotHealthInfoEvent) {
        //机器人健康信息
    }

    @Override
    public void getWallUpdate(WallUpdateEvent wallUpdateEvent) {
        //机器人虚拟墙
    }

    @Override
    public void getLineUpdate(LineUpdateEvent lineUpdateEvent) {

    }

    @Override
    public void getRobotStatusUpdate(RobotStatusUpdateEvent robotStatusUpdateEvent) {
        //机器人状态信息
        tvBattery.setText(!robotStatusUpdateEvent.isCharging() ?
                String.format("%d%%", robotStatusUpdateEvent.getBatteryPercentage()) :
                String.format("%d%% | AC on", robotStatusUpdateEvent.getBatteryPercentage()));
        Log.d(TAG, "onEventMainThread: RobotStatusUpdateEvent= " + robotStatusUpdateEvent.getBatteryPercentage());
        tvQuality.setText(Integer.toString(robotStatusUpdateEvent.getLocalizationQuality())+"");
    }

    @Override
    public void getRobotPoseUpdate(RobotPoseUpdateEvent robotPoseUpdateEvent) {
        /**机器人位置信息*
         *
         */
        if (System.currentTimeMillis() - upDataTime > 1000) {
            upDataTime = System.currentTimeMillis();
            if (robotPoseUpdateEvent != null) {
                String poseString = "[" +
                            "X:" + Float.toString(robotPoseUpdateEvent.getPose().getX())+ " " +
                            "Y:" + Float.toString(robotPoseUpdateEvent.getPose().getY()) + " " +
                            "Z:" + Float.toString(robotPoseUpdateEvent.getPose().getZ()) + " " +
                            "Yaw:" + Float.toString(robotPoseUpdateEvent.getPose().getYaw()) + " " +
                            "Pitch:" + Float.toString(robotPoseUpdateEvent.getPose().getPitch()) + " " +
                            "Roll:" + Float.toString(robotPoseUpdateEvent.getPose().getRoll()) +
                        "]";
                tvRobotPose.setText(poseString);
                NetworkUtils.postData(poseString);
            }
        }
    }
    public enum ActionStatusTwo {
        //"failed" 表示机器人动作失败；
        //"aborted"表示机器人动作被打断(主动调用cancel或者在action没有结束得时候启动新的action)；
        //“unreachable”，表示目标点不可达(搜索路径失败)；
        //“unhealthy"表明当前机器人状态不是健康状态,可以通过getRobotHealth(）去看具体的信息；
        // "lifted",表明当前机器人离开地面,
        // "low_localization_quality"表明当前机器人定位质量很低；
    }

    @Override
    public void getMoveActionUpdateEvent(MoveActionUpdateEvent moveActionUpdateEvent) {
        //机器人移动状态
    }

    @Override
    public void getMapUpdateEvent(MapUpdateEvent mapUpdateEvent) {
        //机器人地图更新状态
    }

    @Override
    public void getLaserScanUpdateEvent(LaserScanUpdateEvent laserScanUpdateEvent) {
        //机器人雷达扫描状态
    }

    @Override
    public void getConnectionLostEvent(ConnectionLostEvent connectionLostEvent) {
        //机器人连接丢失
    }

    @Override
    public void getConnectionFailedEvent(ConnectionFailedEvent connectionFailedEvent) {
        //机器人连接失败
    }

    @Override
    public void getConnected(ConnectedEvent connectedEvent) {
        //机器人连接成功
    }

    private void goBackToMainActivity() {
        finish();
    }

    private void showMoveControlPanel() {
        if (controlPanel != null) {
            return;
        }
        controlPanel = new RPMoveControlPanel(this);
        controlPanel.setClickListener(new RPMoveControlPanel.IOnButtonClickListener() {
            @Override
            public void onButtonForwardClicked() {
                if (isWallEditMode) {
                    return;
                }
                aoboRobotManeger.moveBy(MoveDirection.FORWARD);
            }

            @Override
            public void onButtonBackwardClicked() {
                if (isWallEditMode) {
                    return;
                }
                aoboRobotManeger.moveBy(MoveDirection.BACKWARD);
            }

            @Override
            public void onButtonTurnLeftClicked() {
                if (isWallEditMode) {
                    return;
                }
                aoboRobotManeger.moveBy(MoveDirection.TURN_LEFT);
            }

            @Override
            public void onButtonTurnRightClicked() {
                if (isWallEditMode) {
                    return;
                }
                aoboRobotManeger.moveBy(MoveDirection.TURN_RIGHT);

            }

            @Override
            public void onButtonHideClicked() {
                hideMoveControlPanel();
            }
        });

        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.addRule(RelativeLayout.ABOVE, R.id.control_bar);
        lp.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        lp.bottomMargin = 10;

        root.addView(controlPanel, lp);
    }

    private void hideMoveControlPanel() {
        if (controlPanel == null) {
            return;
        }
        controlPanel.setVisibility(View.GONE);
        controlPanel = null;
    }

    private void showVirtualWallEditPanel() {
        if (virtualWallEditPanel != null) {
            return;
        }

        hideMoveControlPanel();

        virtualWallEditPanel = new RPVirtualWallEditPanel(this);
        virtualWallEditPanel.setClickListener(new RPVirtualWallEditPanel.IOnVirtualWallPanelClickListener() {
            @Override
            public void onButtonExitWallEditClicked() {
                textStatus.setText("");
                mapView.clearWallIndicator();
                isWallEditMode = false;
                wallEditMode = MODE_WALL_NONE;

                hideVirtualWallEditPanel();
            }

            @Override
            public void onButtonAddWallClicked() {
                textStatus.setText(getString(R.string.add_wall));
                wallEditMode = MODE_WALL_ADD;
            }

            @Override
            public void onButtonRemoveWallClicked() {
                //点击屏幕的虚拟墙移除
                textStatus.setText(getString(R.string.remove_wall));
                wallEditMode = MODE_WALL_REMOVE;
            }

            @Override
            public void onButtonClearWallsClicked() {
                textStatus.setText(getString(R.string.clear_walls));
                aoboRobotManeger.clearWalls();
                wallEditMode = MODE_WALL_NONE;
            }
        });

        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        lp.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        lp.bottomMargin = 20;

        root.addView(virtualWallEditPanel, lp);

        controlBar.setVisibility(View.INVISIBLE);
    }

    private void hideVirtualWallEditPanel() {
        if (virtualWallEditPanel == null) {
            return;
        }

        virtualWallEditPanel.setVisibility(View.GONE);
        virtualWallEditPanel = null;

        controlBar.setVisibility(View.VISIBLE);
    }

    public class ChangeAnsyncTask extends AsyncTask<Pose, Integer, IMoveAction> {
        @Override
        protected IMoveAction doInBackground(Pose... params) {
            IMoveAction imoveAction = null;
            Log.e(TAG, "onPostExecute-Target point = " + params[0].getX());
            try {
                imoveAction = aoboRobotManeger.moveToPoint(params[0], "map.stcm", false,true);
                if (imoveAction != null) {
                    Log.e(TAG, "onPostExecute-waitUntilDone = ");
                    imoveAction.waitUntilDone();
                    Log.e(TAG, "onPostExecute-waitUntilDone = Execution completed");
                } else {
                    // Handle error
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return imoveAction;
        }

        @Override
        protected void onPostExecute(IMoveAction result) {
            if (result == null) {
                Log.i(TAG, "IMoveAction为空无法执行");
                return;
            }
            try {
                if (result.getStatus().name().equals("ERROR")) {
                    tv_NaviStatus.setText("Error");
                    Log.i(TAG, "IMoveAction无法到达"+"result.getReason()= "+result.getReason());
                }else{
                    tv_NaviStatus.setText("ReachPoint");
                    Log.i(TAG, "到达点位");
                    NetworkUtils.postData("到达点位");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            Log.i(TAG, "onPostExecute-导航指令执行+" + result.getActionName());

            super.onPostExecute(result);
        }
        @Override
        protected void onProgressUpdate(Integer... values) {
            Log.w(TAG, "onProgressUpdatemoveToRoomPoint: " + values[0]);
            super.onProgressUpdate(values[0]);
        }
    }
}
