<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.vetc.aobot.MainActivity">


    <com.aobo.aoborobotsdk.views.controls.RPMapView
        android:id="@+id/map_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <com.aobo.aoborobotsdk.views.controls.RPControlBar
        android:id="@+id/control_bar"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/layout_status"
        android:background="@color/blue"
        android:padding="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/text_sdp_version_title"
            android:text="@string/sdp_version"
            style="@style/StatusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_sdp_version_content"
            tools:text="1.5.0"
            style="@style/StatusText"
            android:layout_toRightOf="@id/text_sdp_version_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_battery_percentage_title"
            android:text="@string/battery_percentage"
            style="@style/StatusText"
            android:layout_below="@id/text_sdp_version_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_battery_percentage_content"
            tools:text="80% | AC on"
            style="@style/StatusText"
            android:layout_alignBaseline="@id/text_battery_percentage_title"
            android:layout_toRightOf="@id/text_battery_percentage_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_connection_status_title"
            android:text="@string/connection_status"
            style="@style/StatusText"
            android:layout_below="@id/text_battery_percentage_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_connection_status_content"
            tools:text="100kb/s"
            style="@style/StatusText"
            android:layout_alignBaseline="@id/text_connection_status_title"
            android:layout_toRightOf="@id/text_connection_status_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_localization_quality_title"
            android:text="Localization quality:"
            style="@style/StatusText"
            android:layout_below="@id/text_connection_status_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/text_localization_quality"
            tools:text="50"
            style="@style/StatusText"
            android:layout_alignBaseline="@id/text_localization_quality_title"
            android:layout_toRightOf="@id/text_localization_quality_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/text_localization_robot_title"

            style="@style/StatusText"
            android:layout_below="@id/text_localization_quality_title"

            android:layout_width="wrap_content"
            android:text="robotPose："
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/text_localization_robot_pose"
            tools:text="xyz"
            style="@style/StatusText"
            android:layout_alignBaseline="@id/text_localization_robot_title"
            android:layout_toRightOf="@id/text_localization_robot_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </RelativeLayout>


    <TextView
        android:id="@+id/text_status"
        tools:text="status text"
        style="@style/StatusText"
        android:padding="2dp"
        android:gravity="right"
        android:layout_alignParentRight="true"
        android:background="@color/light_blue"

        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <RelativeLayout
        android:id="@+id/layout_navi"
        android:background="@color/blue"
        android:padding="2dp"
        android:layout_below="@+id/text_status"
        android:layout_alignParentRight="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/text_robot_navi_status"
            android:text="stop"
            style="@style/StatusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </RelativeLayout>

    <ImageButton
        android:id="@+id/button_stop"
        android:src="@mipmap/button_cancel_action"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:padding="4dp"
        android:background="#99ffffff"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <Button
        android:id="@+id/button_gopoint"

        android:layout_above="@+id/control_bar"
        android:text="GoPoint"
        android:layout_alignParentRight="true"
        android:padding="4dp"
        android:background="#99ffffff"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</RelativeLayout>
