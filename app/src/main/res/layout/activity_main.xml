<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context="com.vetc.aobot.MainActivity">

    <Button
        android:id="@+id/connect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="<PERSON><PERSON><PERSON> n<PERSON><PERSON> bản đ<PERSON>"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center_horizontal"/>

<!--    <Button-->
<!--        android:id="@+id/btnOpenCamera"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Mở Camera"-->
<!--        android:layout_gravity="center_horizontal"-->
<!--        android:layout_marginBottom="16dp"/>-->

<!--    <ImageView-->
<!--        android:id="@+id/imgPreview"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="80dp"-->
<!--        android:scaleType="centerCrop"-->
<!--        android:src="@android:color/darker_gray"/>-->

    <Button
        android:id="@+id/gotoPointA"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Go to Point A"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/gotoPointB"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Go to Point B"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/gotoPointC"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Go to Point C"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/gotoPointD"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Go to Point D"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/openMap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Open Map"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center_horizontal"/>

<!--    <Button-->
<!--        android:id="@+id/btnTestWhisper"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Test Whisper ASR"-->
<!--        android:layout_marginBottom="8dp" />-->
</LinearLayout>